/* Landing Page Styles */
:root {
  /* Research-Based NAROOP Cultural Color Palette */
  /* Inspired by successful Black-centered websites: Black Enterprise, Essence, BET, Spelman */

  /* Primary Foundation Colors - Clean & Professional */
  --color-primary-cream: #FEFCF7; /* Warm white base - welcoming yet professional */
  --color-secondary-cream: #FAF7F0; /* Subtle warm tint for sections */
  --color-neutral-light: #F8F6F1; /* Light neutral for cards/backgrounds */

  /* Text Colors - High Contrast & Accessible */
  --color-text-primary: #1A1A1A; /* Deep charcoal for primary text */
  --color-text-secondary: #4A4A4A; /* Medium gray for secondary text */
  --color-text-muted: #6B6B6B; /* Lighter gray for muted text */

  /* Cultural Heritage Accent Colors - Strategic Use */
  --color-heritage-gold: #D4AF37; /* Rich gold - prosperity & achievement */
  --color-heritage-burgundy: #8B1538; /* Deep burgundy - strength & dignity */
  --color-heritage-emerald: #2E8B57; /* Forest emerald - growth & harmony */
  --color-heritage-sapphire: #1E3A8A; /* Deep sapphire - wisdom & trust */

  /* Warm Accent Colors - Inviting & Approachable */
  --color-warm-coral: #FF6B6B; /* Soft coral for highlights */
  --color-warm-amber: #F59E0B; /* Warm amber for call-to-actions */
  --color-warm-terracotta: #E07A5F; /* Earthy terracotta for warmth */
}

.landing-page {
  min-height: 100vh;
  background:
    radial-gradient(circle at 25% 75%, rgba(212, 175, 55, 0.06) 0%, transparent 40%),
    radial-gradient(circle at 75% 25%, rgba(139, 21, 56, 0.04) 0%, transparent 40%),
    radial-gradient(circle at 50% 50%, rgba(46, 139, 87, 0.03) 0%, transparent 60%),
    linear-gradient(135deg, var(--color-primary-cream) 0%, var(--color-secondary-cream) 50%, var(--color-neutral-light) 100%);
  color: var(--color-text-primary);
  overflow-x: hidden;
  position: relative;
}

/* Header */
.landing-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: rgba(254, 252, 247, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(212, 175, 55, 0.15);
  transition: all var(--transition-normal);
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-md) var(--space-lg);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-logo .logo-text {
  font-size: var(--text-xl);
  font-weight: 900;
  color: var(--color-heritage-burgundy);
  text-shadow: none;
  letter-spacing: -0.02em;
}

.header-signin {
  background: linear-gradient(135deg, var(--color-heritage-gold) 0%, var(--color-warm-amber) 100%);
  border: 2px solid var(--color-heritage-gold);
  color: var(--color-text-primary);
  padding: var(--space-md) var(--space-xl);
  border-radius: 50px; /* Pill-shaped design */
  font-weight: 700;
  font-size: var(--text-base);
  cursor: pointer;
  transition: all var(--transition-normal);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 15px rgba(212, 175, 55, 0.25);
  min-height: 48px; /* Increased prominence */
}

.header-signin:hover {
  background: linear-gradient(135deg, var(--color-warm-amber) 0%, var(--color-heritage-gold) 100%);
  color: var(--color-text-primary);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(212, 175, 55, 0.35);
}

/* Section spacing and transitions */
.landing-page section {
  position: relative;
}

.landing-page section:not(:last-child) {
  margin-bottom: var(--space-xl);
}

/* Hero Section */
.landing-hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: calc(var(--space-xl) + 80px) var(--space-lg) var(--space-xl);
  position: relative;
}

.landing-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 30%, rgba(212, 175, 55, 0.08) 0%, transparent 40%),
    radial-gradient(circle at 80% 70%, rgba(139, 21, 56, 0.06) 0%, transparent 40%),
    radial-gradient(circle at 50% 90%, rgba(46, 139, 87, 0.04) 0%, transparent 50%);
  pointer-events: none;
}

.hero-content {
  max-width: 1200px;
  width: 100%;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-2xl);
  align-items: center;
}

.hero-text {
  z-index: 2;
}

.hero-title {
  margin: 0 0 var(--space-xl) 0;
}

.brand-name {
  display: block;
  font-size: clamp(3rem, 8vw, 5rem);
  font-weight: 900;
  letter-spacing: -0.02em;
  /* Cohesive cultural gradient using research-based colors */
  background: linear-gradient(135deg, var(--color-heritage-burgundy) 0%, var(--color-heritage-sapphire) 50%, var(--color-heritage-gold) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: var(--space-sm);
  text-shadow: none;
}

.brand-subtitle {
  display: block;
  font-size: clamp(1.2rem, 3vw, 1.8rem);
  font-weight: 500;
  color: var(--color-heritage-emerald);
  text-shadow: none;
  font-style: italic;
}

.hero-description {
  font-size: var(--text-lg);
  line-height: 1.7;
  margin: 0 0 var(--space-2xl) 0;
  color: var(--color-text-secondary);
  text-shadow: none;
  max-width: 600px;
}

/* Hero actions removed - Sign In moved to header */

.cta-primary, .cta-secondary {
  padding: var(--space-lg) var(--space-2xl);
  border-radius: 50px; /* Pill-shaped design */
  font-size: var(--text-lg);
  font-weight: 700;
  cursor: pointer;
  transition: all var(--transition-normal);
  border: none;
  min-height: 56px; /* Increased for better prominence */
  min-width: 180px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.cta-primary {
  background: linear-gradient(135deg, var(--color-heritage-burgundy) 0%, var(--color-heritage-sapphire) 100%);
  color: white;
  box-shadow: 0 8px 25px rgba(139, 21, 56, 0.25);
  border: 2px solid transparent;
}

.cta-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(139, 21, 56, 0.35);
  background: linear-gradient(135deg, var(--color-heritage-sapphire) 0%, var(--color-heritage-burgundy) 100%);
}

.cta-secondary {
  background: rgba(254, 252, 247, 0.95);
  color: var(--color-heritage-emerald);
  border: 2px solid var(--color-heritage-emerald);
  backdrop-filter: blur(10px);
}

.cta-secondary:hover {
  background: var(--color-heritage-emerald);
  color: white;
  border-color: var(--color-heritage-emerald);
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(46, 139, 87, 0.25);
}

.hero-visual {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  position: relative;
}

.hero-visual::before {
  content: '';
  position: absolute;
  top: -2rem;
  right: -2rem;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--color-heritage-gold) 0%, var(--color-warm-amber) 100%);
  border-radius: 0; /* Perfect square */
  transform: rotate(45deg);
  opacity: 0.12;
  animation: float 6s ease-in-out infinite;
}

.hero-visual::after {
  content: '';
  position: absolute;
  bottom: -1rem;
  left: -1rem;
  width: 60px;
  height: 60px;
  background: var(--color-heritage-emerald);
  border-radius: 50%; /* Perfect circle */
  opacity: 0.10;
  animation: float 4s ease-in-out infinite reverse;
}

.hero-icon {
  font-size: clamp(4rem, 10vw, 8rem);
  margin-bottom: var(--space-lg);
  animation: float 3s ease-in-out infinite;
  filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.3));
  transition: filter var(--transition-normal);
  cursor: default;
}

.hero-icon:hover {
  filter: drop-shadow(0 0 30px rgba(255, 215, 0, 0.5));
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

.hero-tagline {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--color-heritage-sapphire);
  letter-spacing: 2px;
  text-transform: uppercase;
  text-shadow: none;
  background: rgba(254, 252, 247, 0.9);
  padding: var(--space-sm) var(--space-md);
  border-radius: 25px; /* Pill-shaped */
  backdrop-filter: blur(10px);
  border: 1px solid rgba(30, 58, 138, 0.15);
}

/* Scroll Indicator */
.scroll-indicator {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
  cursor: pointer;
  transition: all var(--transition-normal);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.scroll-indicator:hover .scroll-chevron {
  transform: translateY(2px);
  opacity: 1;
}

.scroll-indicator:focus {
  outline: 2px solid var(--color-secondary);
  outline-offset: 4px;
  border-radius: var(--radius-sm);
}

.scroll-text {
  font-size: 0.875rem;
  color: var(--color-heritage-emerald);
  font-weight: 600;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  text-shadow: none;
  background: rgba(254, 252, 247, 0.95);
  padding: 0.25rem 0.5rem;
  border-radius: 15px; /* Pill-shaped */
  margin-bottom: 0.25rem;
  border: 1px solid rgba(46, 139, 87, 0.15);
}

.scroll-chevron {
  width: 32px; /* Increased size for better prominence */
  height: 32px;
  border: 3px solid var(--color-heritage-gold);
  border-radius: 0; /* Perfect square */
  transform: rotate(45deg);
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-heritage-gold);
  backdrop-filter: blur(10px);
  transition: all var(--transition-normal);
  opacity: 0.85;
  animation: pulse 2s infinite;
}

.scroll-chevron::after {
  content: '';
  width: 8px;
  height: 8px;
  border-right: 2px solid var(--color-secondary);
  border-bottom: 2px solid var(--color-secondary);
  transform: rotate(45deg) translateY(-1px);
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Features Section */
.landing-features {
  background: var(--color-secondary-cream);
  color: var(--color-text-primary);
  padding: var(--space-2xl) var(--space-lg);
  position: relative;
}

.landing-features::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-heritage-gold) 0%, var(--color-heritage-sapphire) 100%);
}

.features-container {
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
}

.features-container h2 {
  font-size: var(--text-3xl);
  font-weight: 700;
  margin: 0 0 var(--space-md) 0;
  color: var(--color-heritage-burgundy);
}

.features-container::after {
  content: '';
  display: block;
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-secondary) 100%);
  margin: var(--space-lg) auto var(--space-2xl) auto;
  border-radius: 2px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-xl);
  margin-top: var(--space-2xl);
}

.feature-card {
  background: var(--color-primary-cream);
  padding: var(--space-xl);
  border-radius: 25px; /* More rounded for modern look */
  text-align: center;
  transition: transform var(--transition-normal);
  border: 2px solid rgba(46, 139, 87, 0.08);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.04);
}

.feature-card:hover {
  transform: translateY(-5px);
  border-color: var(--color-heritage-emerald);
  box-shadow: 0 15px 35px rgba(46, 139, 87, 0.15);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: var(--space-lg);
}

.feature-card h3 {
  font-size: var(--text-xl);
  font-weight: 600;
  margin: 0 0 var(--space-md) 0;
  color: #1a1a1a; /* Darker color for better contrast */
}

.feature-card p {
  color: #333333; /* Darker gray for better readability */
  line-height: 1.6;
  margin: 0;
  font-weight: 400;
}

/* Launch Section */
.landing-launch {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: white;
  padding: var(--space-2xl) var(--space-lg);
}

.launch-container {
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
}

.launch-container h2 {
  font-size: var(--text-3xl);
  font-weight: 700;
  margin: 0 0 var(--space-2xl) 0;
}

.launch-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-2xl);
  align-items: start;
  margin-bottom: var(--space-2xl);
}

.launch-message {
  text-align: left;
}

.launch-icon {
  font-size: 4rem;
  margin-bottom: var(--space-lg);
}

.launch-message h3 {
  font-size: var(--text-2xl);
  font-weight: 700;
  margin: 0 0 var(--space-lg) 0;
  color: var(--color-secondary);
}

.launch-message p {
  font-size: var(--text-lg);
  line-height: 1.7;
  opacity: 0.95;
  margin: 0;
}

.launch-features {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.launch-feature {
  display: flex;
  align-items: flex-start;
  gap: var(--space-md);
  text-align: left;
  background: rgba(255, 255, 255, 0.1);
  padding: var(--space-lg);
  border-radius: var(--radius-lg);
  backdrop-filter: blur(10px);
}

.feature-emoji {
  font-size: 2rem;
  flex-shrink: 0;
}

.launch-feature h4 {
  margin: 0 0 var(--space-xs) 0;
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--color-secondary);
}

.launch-feature p {
  margin: 0;
  opacity: 0.9;
  line-height: 1.5;
}

.stats-note {
  background: rgba(255, 255, 255, 0.1);
  padding: var(--space-lg);
  border-radius: var(--radius-lg);
  backdrop-filter: blur(10px);
  border-left: 4px solid var(--color-secondary);
}

.stats-note p {
  margin: 0;
  font-style: italic;
  opacity: 0.9;
  color: #ecf0f1;
}

/* About Us Section */
.landing-about {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  color: #2d3748;
  padding: var(--space-2xl) var(--space-lg);
  position: relative;
  overflow: hidden;
}

.landing-about::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 60 60"><defs><pattern id="about-pattern" width="60" height="60" patternUnits="userSpaceOnUse"><circle cx="30" cy="30" r="1" fill="%23e63946" opacity="0.1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23about-pattern)"/></svg>');
  pointer-events: none;
}

.about-container {
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

.about-header {
  text-align: center;
  margin-bottom: var(--space-2xl);
}

.about-header h2 {
  font-size: var(--text-3xl);
  font-weight: 800;
  color: #2d3748;
  margin: 0 0 var(--space-md) 0;
  background: linear-gradient(135deg, #e63946 0%, #dc2626 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.about-subtitle {
  font-size: var(--text-lg);
  color: #4a5568;
  margin: 0;
  font-weight: 500;
  opacity: 0.9;
}

.about-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-2xl);
  margin-bottom: var(--space-2xl);
}

.founder-story {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-lg);
}

.story-card {
  background: white;
  border-radius: var(--radius-lg);
  padding: var(--space-xl);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transition: all var(--transition-normal);
  border-left: 4px solid var(--color-primary);
  position: relative;
  overflow: hidden;
  animation: fadeInUp 0.6s ease-out;
  animation-fill-mode: both;
}

.story-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-accent-green) 0%, var(--color-secondary) 100%);
}

.story-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
}

.story-card:nth-child(1) {
  animation-delay: 0.1s;
}

.story-card:nth-child(2) {
  animation-delay: 0.2s;
}

.story-card:nth-child(3) {
  animation-delay: 0.3s;
}

.story-icon {
  font-size: var(--text-3xl);
  margin-bottom: var(--space-md);
  display: block;
}

.story-card h3 {
  font-size: var(--text-xl);
  font-weight: 700;
  color: #2d3748;
  margin: 0 0 var(--space-md) 0;
}

.story-card p {
  font-size: var(--text-base);
  line-height: 1.6;
  color: #4a5568;
  margin: 0;
}

.mission-statement {
  background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  animation: fadeInUp 0.8s ease-out 0.4s both;
}

.mission-card {
  padding: var(--space-2xl);
  color: white;
  text-align: center;
}

.mission-card h3 {
  font-size: var(--text-2xl);
  font-weight: 800;
  margin: 0 0 var(--space-lg) 0;
  color: var(--color-secondary);
}

.mission-card p {
  font-size: var(--text-lg);
  line-height: 1.6;
  margin: 0 0 var(--space-xl) 0;
  opacity: 0.95;
}

.mission-values {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--space-md);
  margin-top: var(--space-xl);
}

.value-item {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-md);
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all var(--transition-normal);
}

.value-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

.value-icon {
  font-size: var(--text-lg);
}

.about-closing {
  background: linear-gradient(135deg, var(--color-accent-purple) 0%, var(--color-primary) 100%);
  color: white;
  padding: var(--space-2xl);
  border-radius: var(--radius-lg);
  text-align: center;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  animation: fadeInUp 1s ease-out 0.6s both;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.about-closing h3 {
  font-size: var(--text-2xl);
  font-weight: 800;
  margin: 0 0 var(--space-md) 0;
  color: var(--color-secondary);
}

.about-closing p {
  font-size: var(--text-lg);
  line-height: 1.6;
  margin: 0;
  opacity: 0.95;
}

/* CTA Section */
.landing-cta {
  background: linear-gradient(135deg, var(--color-heritage-burgundy) 0%, var(--color-heritage-sapphire) 100%);
  color: white;
  padding: var(--space-2xl) var(--space-lg);
  text-align: center;
}

.cta-container {
  max-width: 800px;
  margin: 0 auto;
}

.cta-container h2 {
  font-size: var(--text-3xl);
  font-weight: 700;
  margin: 0 0 var(--space-lg) 0;
}

.cta-container p {
  font-size: var(--text-lg);
  line-height: 1.6;
  margin: 0 0 var(--space-2xl) 0;
  opacity: 0.95;
}

.cta-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-lg);
}

.cta-primary.large {
  padding: var(--space-xl) var(--space-2xl);
  font-size: var(--text-2xl); /* Increased size for better prominence */
  background: linear-gradient(135deg, var(--color-heritage-burgundy) 0%, var(--color-heritage-sapphire) 100%);
  color: white;
  box-shadow: 0 10px 30px rgba(139, 21, 56, 0.25);
  font-weight: 800;
  min-height: 64px; /* Increased prominence */
  border-radius: 50px; /* Pill-shaped */
  text-transform: uppercase;
  letter-spacing: 1px;
}

.cta-primary.large:hover {
  transform: translateY(-4px);
  box-shadow: 0 15px 40px rgba(139, 21, 56, 0.35);
  background: linear-gradient(135deg, var(--color-heritage-sapphire) 0%, var(--color-heritage-burgundy) 100%);
}

.cta-note {
  margin: 0;
  color: #FFFFFF; /* Pure white for better contrast */
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
}

.guest-note {
  margin-top: var(--space-md);
  padding: var(--space-sm) var(--space-md);
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md);
  border-left: 3px solid var(--color-secondary);
  color: rgba(255, 255, 255, 0.9);
  font-size: var(--text-sm);
  line-height: 1.5;
  backdrop-filter: blur(10px);
}

.guest-note strong {
  color: var(--color-secondary);
}

.link-button {
  background: linear-gradient(135deg, var(--color-heritage-gold) 0%, var(--color-warm-amber) 100%);
  border: 2px solid var(--color-heritage-gold);
  color: var(--color-text-primary);
  text-decoration: none;
  cursor: pointer;
  font-size: inherit;
  font-weight: 600;
  margin-left: var(--space-xs);
  transition: all var(--transition-fast);
  text-shadow: none;
  padding: var(--space-xs) var(--space-md);
  border-radius: 20px; /* Pill-shaped */
  display: inline-block;
}

.link-button:hover {
  background: linear-gradient(135deg, var(--color-warm-amber) 0%, var(--color-heritage-gold) 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(212, 175, 55, 0.25);
}

/* Footer */
.landing-footer {
  background: linear-gradient(135deg, var(--color-heritage-emerald) 0%, var(--color-heritage-sapphire) 100%);
  color: white;
  padding: var(--space-xl) var(--space-lg) var(--space-lg) var(--space-lg);
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-lg);
  flex-wrap: wrap;
  gap: var(--space-lg);
}

.footer-brand h3 {
  margin: 0;
  font-size: var(--text-xl);
  color: var(--color-secondary);
}

.footer-brand p {
  margin: var(--space-xs) 0 0 0;
  opacity: 0.8;
  font-style: italic;
}

.footer-links {
  display: flex;
  gap: var(--space-lg);
  flex-wrap: wrap;
}

.footer-links a {
  color: white;
  text-decoration: none;
  transition: color var(--transition-fast);
}

.footer-links a:hover {
  color: var(--color-secondary);
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--space-lg);
  border-top: 1px solid #333;
  flex-wrap: wrap;
  gap: var(--space-md);
}

.footer-bottom p {
  margin: 0;
  opacity: 0.7;
  font-size: var(--text-sm);
}

.version-info {
  background: rgba(255, 255, 255, 0.1);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-md);
  font-size: var(--text-xs);
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-content {
    grid-template-columns: 1fr;
    text-align: center;
    gap: var(--space-xl);
  }

  .hero-actions {
    justify-content: center;
  }

  /* Override the single button centering for mobile */
  .hero-actions:has(.cta-secondary:only-child) {
    justify-content: center;
  }

  .cta-primary, .cta-secondary {
    width: 100%;
    max-width: 300px;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: var(--space-lg);
  }

  .launch-content {
    grid-template-columns: 1fr;
    gap: var(--space-xl);
  }

  .launch-message {
    text-align: center;
  }

  .footer-content {
    flex-direction: column;
    text-align: center;
  }

  .footer-bottom {
    flex-direction: column;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .landing-hero {
    padding: var(--space-lg) var(--space-md);
  }

  .landing-features,
  .landing-stats,
  .landing-cta {
    padding: var(--space-xl) var(--space-md);
  }

  .launch-features {
    gap: var(--space-md);
  }

  .launch-feature {
    padding: var(--space-md);
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .cta-primary, .cta-secondary {
    width: 100%;
  }

  .scroll-indicator {
    bottom: 1.5rem;
  }

  .scroll-text {
    font-size: 0.75rem;
  }

  .scroll-chevron {
    width: 20px;
    height: 20px;
  }

  .header-content {
    padding: var(--space-sm) var(--space-md);
  }

  .header-logo .logo-text {
    font-size: var(--text-lg);
  }

  .header-signin {
    padding: var(--space-xs) var(--space-md);
    font-size: 0.75rem;
  }
}

/* Tablet Styles for About Us */
@media (min-width: 768px) {
  .about-content {
    grid-template-columns: 2fr 1fr;
    gap: var(--space-3xl);
    align-items: start;
  }

  .founder-story {
    grid-template-columns: 1fr;
    gap: var(--space-xl);
  }

  .mission-values {
    grid-template-columns: 1fr;
    gap: var(--space-lg);
  }

  .value-item {
    justify-content: center;
    text-align: center;
    flex-direction: column;
    gap: var(--space-xs);
  }

  .about-header h2 {
    font-size: var(--text-4xl);
  }

  .about-subtitle {
    font-size: var(--text-xl);
  }
}

/* Desktop Styles for About Us */
@media (min-width: 1024px) {
  .about-content {
    grid-template-columns: 1fr;
    gap: var(--space-2xl);
  }

  .founder-story {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-xl);
  }

  .mission-values {
    grid-template-columns: repeat(4, 1fr);
    gap: var(--space-md);
  }

  .value-item {
    flex-direction: row;
    justify-content: flex-start;
    text-align: left;
    gap: var(--space-sm);
  }

  .story-card {
    padding: var(--space-2xl);
  }

  .mission-card {
    padding: var(--space-3xl);
  }

  .about-header h2 {
    font-size: var(--text-4xl);
  }
}

/* Mobile Styles for About Us */
@media (max-width: 480px) {
  .landing-about {
    padding: var(--space-xl) var(--space-md);
  }

  .about-header h2 {
    font-size: var(--text-2xl);
  }

  .about-subtitle {
    font-size: var(--text-base);
  }

  .story-card {
    padding: var(--space-lg);
  }

  .story-card h3 {
    font-size: var(--text-lg);
  }

  .story-card p {
    font-size: var(--text-sm);
  }

  .mission-card {
    padding: var(--space-lg);
  }

  .mission-card h3 {
    font-size: var(--text-xl);
  }

  .mission-card p {
    font-size: var(--text-base);
  }

  .mission-values {
    grid-template-columns: 1fr;
    gap: var(--space-sm);
  }

  .value-item {
    padding: var(--space-sm);
    font-size: var(--text-sm);
  }

  .about-closing {
    padding: var(--space-lg);
  }

  .about-closing h3 {
    font-size: var(--text-xl);
  }

  .about-closing p {
    font-size: var(--text-base);
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .landing-page {
    background: #000;
    color: #fff;
  }

  .landing-features {
    background: #fff;
    color: #000;
  }

  .feature-card {
    background: #f0f0f0;
    border: 2px solid #000;
  }

  .cta-primary {
    background: #fff;
    color: #000;
    border: 2px solid #000;
  }

  .cta-secondary {
    background: transparent;
    border: 2px solid #fff;
  }

  .landing-about {
    background: #fff;
    color: #000;
  }

  .story-card {
    background: #f0f0f0;
    border: 2px solid #000;
    color: #000;
  }

  .mission-statement {
    background: #000;
    color: #fff;
  }

  .mission-card {
    color: #fff;
  }

  .value-item {
    background: rgba(0, 0, 0, 0.1);
    border: 1px solid #000;
    color: #000;
  }

  .about-closing {
    background: #000;
    color: #fff;
    border: 2px solid #fff;
  }
}
