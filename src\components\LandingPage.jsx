import React, { useState } from 'react';
import Login from './Login';
import Signup from './Signup';
import PasswordReset from './PasswordReset';
import CommunitySection from './CommunitySection';
import { getFormattedVersion } from '../utils/version';
import { useAuth } from '../AuthContext';
import './LandingPage.css';

const LandingPage = () => {
  const [showLogin, setShowLogin] = useState(false);
  const [showSignup, setShowSignup] = useState(false);
  const [showReset, setShowReset] = useState(false);
  const { enableGuestMode } = useAuth();

  const handleGetStarted = () => {
    setShowSignup(true);
  };

  const handleSignIn = () => {
    setShowLogin(true);
  };

  const handleBrowseAsGuest = () => {
    enableGuestMode();
  };

  const handleBackToLanding = () => {
    setShowLogin(false);
    setShowSignup(false);
    setShowReset(false);
  };

  const handleScrollToFeatures = () => {
    const featuresSection = document.querySelector('.landing-features');
    if (featuresSection) {
      featuresSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  // If any auth modal is open, show it
  if (showReset) {
    return <PasswordReset onBackToLogin={() => setShowReset(false)} />;
  }
  
  if (showLogin) {
    return (
      <Login
        onSwitchToSignup={() => {
          setShowLogin(false);
          setShowSignup(true);
        }}
        onSwitchToReset={() => setShowReset(true)}
        onBackToLanding={handleBackToLanding}
      />
    );
  }
  
  if (showSignup) {
    return (
      <Signup
        onSwitchToLogin={() => {
          setShowSignup(false);
          setShowLogin(true);
        }}
        onBackToLanding={handleBackToLanding}
      />
    );
  }

  return (
    <div className="landing-page">
      {/* Header */}
      <header className="landing-header">
        <div className="header-content">
          <div className="header-logo">
            <span className="logo-text">NAROOP</span>
          </div>
          <button className="header-signin" onClick={handleSignIn}>
            Sign In
          </button>
        </div>
      </header>

      {/* Hero Section */}
      <section className="landing-hero">
        <div className="hero-content">
          <div className="hero-text">
            <h1 className="hero-title">
              <span className="brand-name">NAROOP</span>
              <span className="brand-subtitle">Narrative of Our People</span>
            </h1>
            <p className="hero-description">
              Discover a space where authentic Black stories create positive change.
              Explore how our community connects, grows, and reshapes the narrative about who we are.
              Your journey of discovery starts here—scroll down to learn what makes us different.
            </p>
          </div>
          <div className="hero-visual">
            <div className="hero-icon">✊🏾</div>
            <div className="hero-tagline">Unity • Growth • Empowerment</div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div
          className="scroll-indicator"
          onClick={handleScrollToFeatures}
          role="button"
          tabIndex={0}
          aria-label="Scroll down to learn more about NAROOP"
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              handleScrollToFeatures();
            }
          }}
        >
          <span className="scroll-text">Explore</span>
          <div className="scroll-chevron"></div>
        </div>
      </section>

      {/* Features Overview */}
      <section className="landing-features">
        <div className="features-container">
          <h2>Discover What Makes NAROOP Special</h2>
          <div className="features-grid">
            <div className="feature-card">
              <div className="feature-icon">📖</div>
              <h3>Share Your Story</h3>
              <p>
                Share your experiences, celebrate achievements, and inspire others 
                with your unique narrative in our supportive community.
              </p>
            </div>
            
            <div className="feature-card">
              <div className="feature-icon">💰</div>
              <h3>Economic Empowerment</h3>
              <p>
                Access resources, connect with mentors, and discover opportunities 
                for financial growth and business development.
              </p>
            </div>
            
            <div className="feature-card">
              <div className="feature-icon">🗣️</div>
              <h3>Community Dialogue</h3>
              <p>
                Engage in meaningful conversations, share perspectives, and build 
                connections with community members who understand your journey.
              </p>
            </div>
            
            <div className="feature-card">
              <div className="feature-icon">🤝</div>
              <h3>Support Network</h3>
              <p>
                Find help when you need it, offer support to others, and be part 
                of a caring community that lifts each other up.
              </p>
            </div>
            
            <div className="feature-card">
              <div className="feature-icon">✊🏾</div>
              <h3>Community Activism</h3>
              <p>
                Organize, advocate, and create positive change in your community 
                through collective action and shared purpose.
              </p>
            </div>
            
            <div className="feature-card">
              <div className="feature-icon">🌟</div>
              <h3>Kids Zone</h3>
              <p>
                A safe, educational space for children with positive messaging, 
                role models, and age-appropriate content that builds confidence.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* About Us Section */}
      <section className="landing-about">
        <div className="about-container">
          <div className="about-header">
            <h2>Our Story</h2>
            <p className="about-subtitle">
              The authentic journey behind NAROOP - Narrative of Our People
            </p>
          </div>

          <div className="about-content">
            <div className="founder-story">
              <div className="story-card">
                <div className="story-icon">✊🏾</div>
                <h3>Born from Experience</h3>
                <p>
                  We are two African American brothers from the inner city who experienced
                  daily struggles firsthand. Growing up, we witnessed the challenges our
                  community faced and felt the weight of negative stereotypes that didn't
                  represent who we truly are.
                </p>
              </div>

              <div className="story-card">
                <div className="story-icon">💡</div>
                <h3>A Vision for Change</h3>
                <p>
                  Our personal experiences motivated us to create something different - a
                  space where positive stories could flourish and where the narrative about
                  Black people could shift from stereotypes to authentic representation of
                  our strength, resilience, and greatness.
                </p>
              </div>

              <div className="story-card">
                <div className="story-icon">🌟</div>
                <h3>The Meaning Behind NAROOP</h3>
                <p>
                  <strong>NAROOP</strong> stands for <strong>"Narrative of Our People"</strong> -
                  a platform dedicated to reshaping how Black stories are told and heard.
                  We believe that when we control our own narrative, we can inspire positive
                  change and build stronger communities.
                </p>
              </div>
            </div>

            <div className="mission-statement">
              <div className="mission-card">
                <h3>Our Mission</h3>
                <p>
                  To create a space for positive growth and development within the Black
                  community through authentic storytelling, community collaboration, and
                  unity. We're building something great together - not just a platform,
                  but a movement that celebrates Black excellence and empowers voices
                  that deserve to be heard.
                </p>
                <div className="mission-values">
                  <div className="value-item">
                    <span className="value-icon">🤝</span>
                    <span>Community Unity</span>
                  </div>
                  <div className="value-item">
                    <span className="value-icon">📈</span>
                    <span>Positive Growth</span>
                  </div>
                  <div className="value-item">
                    <span className="value-icon">🎯</span>
                    <span>Authentic Stories</span>
                  </div>
                  <div className="value-item">
                    <span className="value-icon">💪🏾</span>
                    <span>Empowerment</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="about-closing">
            <h3>Building Something Great Together</h3>
            <p>
              We're not just creating a platform—we're building a movement. Every story shared,
              every connection made, and every voice lifted contributes to reshaping how the
              world sees and celebrates Black excellence. This is our narrative, and this is
              our time.
            </p>
          </div>
        </div>
      </section>

      {/* Community Section - Future-proof design */}
      <CommunitySection
        showStats={false}
        launchMode={true}
        // When real stats become available, use:
        // showStats={true}
        // stats={{
        //   stories: 1250,
        //   members: 850,
        //   successStories: 75,
        //   support: "24/7",
        //   highlights: [...]
        // }}
        // launchMode={false}
      />

      {/* Call to Action */}
      <section className="landing-cta">
        <div className="cta-container">
          <h2>Your Story Starts Here</h2>
          <p>
            Join NAROOP and be part of a community that celebrates authentic Black voices,
            shares powerful stories, and builds lasting connections. As a founding member,
            you'll help shape the future of positive representation in our community.
          </p>
          <div className="cta-actions">
            <button className="cta-primary large" onClick={handleGetStarted}>
              Start Your Journey
            </button>
            <button className="cta-secondary large" onClick={handleBrowseAsGuest}>
              Browse as Guest
            </button>
            <p className="cta-note">
              Already have an account?
              <button className="link-button" onClick={handleSignIn}>
                Sign in here
              </button>
            </p>
            <p className="guest-note">
              <small>
                💡 <strong>Guest browsing:</strong> Explore stories and community content without signing up.
                You'll need to create an account to share stories, react, or connect with others.
              </small>
            </p>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="landing-footer">
        <div className="footer-container">
          <div className="footer-content">
            <div className="footer-brand">
              <h3>NAROOP</h3>
              <p>Narrative of Our People</p>
            </div>
            <div className="footer-links">
              <a href="#about">About</a>
              <a href="#privacy">Privacy</a>
              <a href="#terms">Terms</a>
              <a href="#contact">Contact</a>
            </div>
          </div>
          <div className="footer-bottom">
            <p>&copy; 2025 NAROOP. All rights reserved.</p>
            <span className="version-info">{getFormattedVersion()}</span>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default LandingPage;
